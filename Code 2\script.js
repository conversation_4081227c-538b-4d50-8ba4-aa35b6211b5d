// Code 1 variables and functions
var openCiphers = ["Ordinal", "Reduction", "Reverse", "Reverse Reduction"];
var ciphersOn = [];
var allCiphers = [];
var sHistory = [];
var opt_NumCalculation = "Reduced";

// Declare ciphers object (loaded from ciphers.js)
var ciphers;

// Page launch function to match Code 1
function Page_Launch() {
    Build_Ciphers();
}

// Get search field value like Code 1
function sVal() {
    var phr = document.getElementById("SearchField").value.trim();
    return phr;
}

// Field change function like Code 1
function FieldChange(impVal) {
    Populate_Sums(impVal);
}

// Navigation history function
function navHistory(event) {
    // Handle Enter key
    if (event.key === 'Enter' || event.keyCode === 13) {
        const inputElement = document.getElementById('input-text');
        const value = inputElement ? inputElement.value.trim() : '';
        if (value && !sHistory.includes(value)) {
            sHistory.push(value);
        }
    }
}

// Build ciphers function
function Build_Ciphers() {
    // Make sure ciphers object is available
    if (typeof ciphers === 'undefined') {
        console.error('Ciphers object not loaded');
        return;
    }

    allCiphers = [];
    for (const key in ciphers) {
        allCiphers.push({
            Nickname: key,
            name: ciphers[key].name,
            category: ciphers[key].category,
            RGB: [255, 255, 255], // Default color
            Gematria: function(text) {
                const cipherKey = this.Nickname;
                const cipher = ciphers[cipherKey];
                if (cipher) {
                    return processTextWithCipher(text, cipher).totalValue;
                }
                return 0;
            }
        });
    }
    Build_Open_Ciphers();
}

// Build open ciphers
function Build_Open_Ciphers() {
    ciphersOn = [];
    for (let x = 0; x < openCiphers.length; x++) {
        for (let z = 0; z < allCiphers.length; z++) {
            if (allCiphers[z].Nickname === openCiphers[x]) {
                ciphersOn.push(allCiphers[z]);
                break;
            }
        }
    }
    Build_Table();
}

// Populate sums function like Code 1
function Populate_Sums(impVal) {
    if (!impVal) return;

    // For Code 2 frontend, trigger the main calculation
    calculateGematria();
}

// Build table function
function Build_Table() {
    let retStr = '<center><table id="GemTable"><tr>';

    for (let x = 0; x < ciphersOn.length; x++) {
        const cipher = ciphersOn[x];
        retStr += '<td style="text-align: center; padding: 5px; border: 1px solid #777;">';
        retStr += '<div style="color: rgb(' + (cipher.RGB || [255,255,255]).join(',') + ');">' + cipher.name + '</div>';
        retStr += '<div id="' + cipher.Nickname + '_Sum" style="font-size: 120%; font-weight: bold;">0</div>';
        retStr += '</td>';
    }

    retStr += '</tr></table></center>';
    document.getElementById("Gematria_Table").innerHTML = retStr;
}

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements - keep Code 2 frontend structure
    const inputText = document.getElementById('input-text');
    const resultsContainer = document.getElementById('results-container');
    const summaryContainer = document.getElementById('summary-container');
    const selectAllBtn = document.getElementById('select-all-btn');
    const clearAllBtn = document.getElementById('clear-all-btn');
    const cipherFiltersContainer = document.getElementById('cipher-filters');
    const cipherSearchInput = document.getElementById('cipher-search');

    // Store selected ciphers
    let selectedCiphers = {};

    // Populate cipher filters
    function populateCipherFilters() {
        cipherFiltersContainer.innerHTML = '';

        // Group ciphers by category
        const categories = {};

        // Initialize categories
        for (const category in cipherCategories) {
            categories[cipherCategories[category]] = [];
        }

        // Group ciphers by their category
        for (const cipherKey in ciphers) {
            const cipher = ciphers[cipherKey];
            const category = cipher.category || "Other";

            if (!categories[category]) {
                categories[category] = [];
            }

            categories[category].push({
                key: cipherKey,
                cipher: cipher
            });
        }

        // Create category sections and add ciphers
        for (const category in categories) {
            if (categories[category].length === 0) continue;

            // Create category header
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'cipher-category-header';
            categoryHeader.textContent = category;
            categoryHeader.dataset.category = category;
            cipherFiltersContainer.appendChild(categoryHeader);

            // Create container for category items
            const categoryItemsContainer = document.createElement('div');
            categoryItemsContainer.className = 'cipher-category-items';
            categoryItemsContainer.dataset.category = category;
            cipherFiltersContainer.appendChild(categoryItemsContainer);

            // Add click event to toggle category items
            categoryHeader.addEventListener('click', function(e) {
                // Prevent checkbox clicks from triggering this
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'LABEL') {
                    return;
                }

                const categoryItems = document.querySelector(`.cipher-category-items[data-category="${category}"]`);
                if (categoryItems.style.display === 'none') {
                    categoryItems.style.display = 'block';
                    this.classList.remove('collapsed');
                } else {
                    categoryItems.style.display = 'none';
                    this.classList.add('collapsed');
                }
            });

            // Add ciphers for this category
            categories[category].forEach(item => {
                const cipherKey = item.key;
                const cipher = item.cipher;

                // Create filter item
                const filterItem = document.createElement('div');
                filterItem.className = 'cipher-filter-item';

                // Create checkbox
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = cipherKey.replace(/_/g, '-');
                checkbox.checked = true;

                // Create label
                const label = document.createElement('label');
                label.htmlFor = checkbox.id;
                label.textContent = cipher.name;

                // Add to filter item
                filterItem.appendChild(checkbox);
                filterItem.appendChild(label);

                // Add to category container
                categoryItemsContainer.appendChild(filterItem);

                // Store initial state
                selectedCiphers[cipherKey] = true;

                // Add event listener
                checkbox.addEventListener('change', function() {
                    selectedCiphers[cipherKey] = this.checked;
                    calculateGematria();
                });
            });
        }
    }

    // Initialize by selecting all ciphers
    function initializeFilters() {
        populateCipherFilters();

        // Add event listeners to buttons
        selectAllBtn.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.cipher-filter-item input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                const cipherKey = checkbox.id.replace(/-/g, '_');
                selectedCiphers[cipherKey] = true;
            });
            calculateGematria();
        });

        clearAllBtn.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.cipher-filter-item input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                const cipherKey = checkbox.id.replace(/-/g, '_');
                selectedCiphers[cipherKey] = false;
            });
            calculateGematria();
        });

        // Add select all and clear all buttons for each category
        document.querySelectorAll('.cipher-category-header').forEach(header => {
            const categoryName = header.dataset.category;

            // Add right-click functionality to toggle all ciphers in that category
            header.addEventListener('contextmenu', function(e) {
                e.preventDefault(); // Prevent the default context menu

                const categoryContainer = document.querySelector(`.cipher-category-items[data-category="${categoryName}"]`);

                if (!categoryContainer) return;

                const categoryItems = categoryContainer.querySelectorAll('.cipher-filter-item');

                // Check if all items are checked
                const allChecked = Array.from(categoryItems).every(item =>
                    item.querySelector('input[type="checkbox"]').checked
                );

                // Toggle all items
                categoryItems.forEach(item => {
                    const checkbox = item.querySelector('input[type="checkbox"]');
                    checkbox.checked = !allChecked;
                    const cipherKey = checkbox.id.replace(/-/g, '_');
                    selectedCiphers[cipherKey] = !allChecked;
                });

                calculateGematria();
            });
        });

        // Add search functionality
        cipherSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const filterItems = document.querySelectorAll('.cipher-filter-item');
            const categoryHeaders = document.querySelectorAll('.cipher-category-header');
            const categoryContainers = document.querySelectorAll('.cipher-category-items');

            // Create a map to track which categories have visible items
            const categoriesWithVisibleItems = {};

            // Initialize all categories as having no visible items
            categoryHeaders.forEach(header => {
                const category = header.dataset.category;
                categoriesWithVisibleItems[category] = false;
            });

            // Check each filter item
            filterItems.forEach(item => {
                const label = item.querySelector('label');
                const cipherName = label.textContent.toLowerCase();

                // Get the parent category container
                const categoryContainer = item.closest('.cipher-category-items');
                const category = categoryContainer ? categoryContainer.dataset.category : null;

                if (cipherName.includes(searchTerm) || (category && category.toLowerCase().includes(searchTerm))) {
                    item.style.display = 'flex';
                    if (category) {
                        categoriesWithVisibleItems[category] = true;
                    }
                } else {
                    item.style.display = 'none';
                }
            });

            // Show/hide category headers and containers based on whether they have visible items
            categoryHeaders.forEach(header => {
                const category = header.dataset.category;
                if (categoriesWithVisibleItems[category]) {
                    header.style.display = 'block';
                    // Find and show the corresponding container
                    const container = document.querySelector(`.cipher-category-items[data-category="${category}"]`);
                    if (container) {
                        container.style.display = 'block';
                        // Make sure the header is not collapsed
                        header.classList.remove('collapsed');
                    }
                } else {
                    header.style.display = 'none';
                    // Find and hide the corresponding container
                    const container = document.querySelector(`.cipher-category-items[data-category="${category}"]`);
                    if (container) {
                        container.style.display = 'none';
                    }
                }
            });

            // If search is empty, restore all categories
            if (searchTerm === '') {
                categoryHeaders.forEach(header => {
                    header.style.display = 'block';
                });
                categoryContainers.forEach(container => {
                    container.style.display = 'block';
                });
            }
        });
    }

    // Set up auto-calculation like Code 1 (immediate, no debounce)
    inputText.addEventListener('input', function() {
        FieldChange(this.value);
    });

    // Add event listener for Enter key and navigation like Code 1
    inputText.addEventListener('keydown', function(e) {
        navHistory(e);
        if (e.key === 'Enter') {
            calculateGematria();
        }
    });

    // Main function to calculate gematria values
    function calculateGematria() {
        const text = inputText.value.trim();

        if (!text) {
            resultsContainer.innerHTML = '<p class="empty-result">Please enter some text to calculate</p>';
            summaryContainer.innerHTML = '<p class="empty-result">Enter text to see summary</p>';
            return;
        }

        // Clear previous results
        resultsContainer.innerHTML = '';
        summaryContainer.innerHTML = '';

        // Store results for summary in the order they are processed
        const summaryResults = [];

        // Process each selected cipher
        for (const cipherKey in ciphers) {
            // Skip if cipher is not selected
            if (!selectedCiphers[cipherKey]) continue;

            const cipher = ciphers[cipherKey];
            const result = processTextWithCipher(text, cipher);

            // Store result for summary
            summaryResults.push({
                name: cipher.name,
                totalValue: result.totalValue,
                category: cipher.category || "Other"
            });

            // Create result element
            const resultElement = createResultElement(text, cipher, result);
            resultsContainer.appendChild(resultElement);
        }

        // Create summary elements in the same order as main results
        createSummaryElements(summaryResults);

        // Show message if no ciphers are selected
        if (resultsContainer.children.length === 0) {
            resultsContainer.innerHTML = '<p class="empty-result">No ciphers selected. Please select at least one cipher from the sidebar.</p>';
            summaryContainer.innerHTML = '<p class="empty-result">No ciphers selected. Please select at least one cipher from the sidebar.</p>';
        }
    }

    // Process text with a specific cipher using Code 1's exact logic
    function processTextWithCipher(text, cipher) {
        let totalValue = 0;
        const wordValues = [];
        const letterBreakdowns = [];

        // Code 1's exact calculation logic
        for (let x = 0; x < text.length; x++) {
            const z = text.charCodeAt(x);
            let value = 0;

            // Check lowercase letters (a-z = 97-122)
            if (z >= 97 && z <= 122) {
                const letter = String.fromCharCode(z);
                value = cipher.values[letter] || 0;
            }
            // Check uppercase letters (A-Z = 65-90)
            else if (z >= 65 && z <= 90) {
                const letter = String.fromCharCode(z + 32); // Convert to lowercase
                value = cipher.values[letter] || 0;
            }
            // Handle numbers (0-9 = 48-57) - Code 1's "Reduced" mode
            else if (z >= 48 && z <= 57) {
                value = z - 48; // Convert char code to number value
            }

            if (value > 0) {
                totalValue += value;
            }
        }

        // For compatibility with existing UI, create word breakdown
        const words = text.split(/\s+/);
        words.forEach(word => {
            let wordValue = 0;
            let letterBreakdown = '';

            for (let i = 0; i < word.length; i++) {
                const z = word.charCodeAt(i);
                let value = 0;
                let displayChar = word[i];

                if (z >= 97 && z <= 122) {
                    value = cipher.values[word[i]] || 0;
                } else if (z >= 65 && z <= 90) {
                    value = cipher.values[word[i].toLowerCase()] || 0;
                } else if (z >= 48 && z <= 57) {
                    value = z - 48;
                }

                if (value > 0) {
                    wordValue += value;
                    letterBreakdown += displayChar + "=" + value + ' ';
                }
            }

            if (wordValue > 0) {
                wordValues.push({
                    word: word,
                    value: wordValue
                });
                letterBreakdowns.push(letterBreakdown.trim());
            }
        });

        return {
            totalValue,
            wordValues,
            letterBreakdowns
        };
    }

    // Create result element for a cipher
    function createResultElement(text, cipher, result) {
        const div = document.createElement('div');
        div.className = 'cipher-result';

        // Add category as a data attribute for potential filtering
        if (cipher.category) {
            div.dataset.category = cipher.category;
        }

        // Create cipher header
        const header = document.createElement('div');
        header.className = 'cipher-header';

        const nameContainer = document.createElement('div');
        nameContainer.className = 'cipher-name-container';

        const nameSpan = document.createElement('span');
        nameSpan.className = 'cipher-name';
        nameSpan.textContent = cipher.name;

        // Add category badge if available
        if (cipher.category) {
            const categoryBadge = document.createElement('span');
            categoryBadge.className = 'cipher-category-badge';
            categoryBadge.textContent = cipher.category;
            nameContainer.appendChild(nameSpan);
            nameContainer.appendChild(categoryBadge);
        } else {
            nameContainer.appendChild(nameSpan);
        }

        const valueSpan = document.createElement('span');
        valueSpan.className = 'cipher-value';
        valueSpan.textContent = result.totalValue;

        header.appendChild(nameContainer);
        header.appendChild(valueSpan);

        // Create cipher description
        const description = document.createElement('div');
        description.className = 'cipher-description';
        description.textContent = cipher.description || '';

        // Create word breakdown section
        const wordBreakdown = document.createElement('div');
        wordBreakdown.className = 'word-breakdown';

        const wordHeading = document.createElement('h4');
        wordHeading.textContent = 'Word Breakdown:';
        wordBreakdown.appendChild(wordHeading);

        const wordList = document.createElement('div');
        wordList.className = 'word-list';

        // Add each word with its value
        result.wordValues.forEach((wordObj, index) => {
            const wordItem = document.createElement('div');
            wordItem.className = 'word-item';

            // Create word header with word and value
            const wordHeader = document.createElement('div');
            wordHeader.className = 'word-header';

            const wordText = document.createElement('span');
            wordText.className = 'word-text';
            wordText.textContent = wordObj.word;

            const wordValue = document.createElement('span');
            wordValue.className = 'word-value';
            wordValue.textContent = wordObj.value;

            wordHeader.appendChild(wordText);
            wordHeader.appendChild(wordValue);
            wordItem.appendChild(wordHeader);

            // Add letter breakdown
            const letterBreakdown = document.createElement('div');
            letterBreakdown.className = 'letter-breakdown';
            letterBreakdown.textContent = result.letterBreakdowns[index];
            wordItem.appendChild(letterBreakdown);

            wordList.appendChild(wordItem);
        });

        wordBreakdown.appendChild(wordList);

        // Assemble the complete result element
        div.appendChild(header);
        div.appendChild(description);
        div.appendChild(wordBreakdown);

        return div;
    }

    // Create summary elements
    function createSummaryElements(summaryResults) {
        // Group summary results by category
        const categorizedResults = {};

        // Initialize categories
        for (const category in cipherCategories) {
            categorizedResults[cipherCategories[category]] = [];
        }

        // Group results by category
        summaryResults.forEach(result => {
            const category = result.category;

            if (!categorizedResults[category]) {
                categorizedResults[category] = [];
            }

            categorizedResults[category].push(result);
        });

        // Create summary items grouped by category
        for (const category in categorizedResults) {
            if (categorizedResults[category].length === 0) continue;

            // Create category header
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'summary-category-header';
            categoryHeader.textContent = category;
            categoryHeader.dataset.category = category;
            summaryContainer.appendChild(categoryHeader);

            // Create container for this category's items
            const categoryItemsContainer = document.createElement('div');
            categoryItemsContainer.className = 'summary-category-items';
            categoryItemsContainer.dataset.category = category;
            summaryContainer.appendChild(categoryItemsContainer);

            // Add items for this category
            categorizedResults[category].forEach(result => {
                const summaryItem = document.createElement('div');
                summaryItem.className = 'summary-item';
                summaryItem.dataset.category = category;

                const nameSpan = document.createElement('span');
                nameSpan.className = 'summary-name';
                nameSpan.textContent = result.name;

                const valuesSpan = document.createElement('span');
                valuesSpan.className = 'summary-values';

                // Only show the total value
                valuesSpan.textContent = result.totalValue.toString();

                summaryItem.appendChild(nameSpan);
                summaryItem.appendChild(valuesSpan);

                categoryItemsContainer.appendChild(summaryItem);
            });

            // Add click event to toggle category items
            categoryHeader.addEventListener('click', function() {
                const categoryItems = document.querySelector(`.summary-category-items[data-category="${category}"]`);
                if (categoryItems.style.display === 'none') {
                    categoryItems.style.display = 'block';
                    this.classList.remove('collapsed');
                } else {
                    categoryItems.style.display = 'none';
                    this.classList.add('collapsed');
                }
            });
        }
    }

    // Initialize like Code 1
    Page_Launch();

    // Initialize filters
    initializeFilters();

    // Initial calculation if there's text in the input
    calculateGematria();
});