<!DOCTYPE html>
<html>
<head>
    <title>Test Code 1 Logic</title>
</head>
<body>
    <h1>Testing Code 1 Calculation Logic</h1>
    <div id="results"></div>
    
    <script src="ciphers.js"></script>
    <script>
        // Test the calculation logic
        function processTextWithCipher(text, cipher) {
            let totalValue = 0;
            const wordValues = [];
            const letterBreakdowns = [];
            
            // Code 1's exact calculation logic
            for (let x = 0; x < text.length; x++) {
                const z = text.charCodeAt(x);
                let value = 0;
                
                // Check lowercase letters (a-z = 97-122)
                if (z >= 97 && z <= 122) {
                    const letter = String.fromCharCode(z);
                    value = cipher.values[letter] || 0;
                }
                // Check uppercase letters (A-Z = 65-90) 
                else if (z >= 65 && z <= 90) {
                    const letter = String.fromCharCode(z + 32); // Convert to lowercase
                    value = cipher.values[letter] || 0;
                }
                // Handle numbers (0-9 = 48-57) - Code 1's "Reduced" mode
                else if (z >= 48 && z <= 57) {
                    value = z - 48; // Convert char code to number value
                }
                
                if (value > 0) {
                    totalValue += value;
                }
            }

            return {
                totalValue,
                wordValues,
                letterBreakdowns
            };
        }
        
        // Test calculations
        const results = document.getElementById('results');
        
        if (typeof ciphers !== 'undefined') {
            // Test "hello" in Ordinal - should be 52
            const helloResult = processTextWithCipher('hello', ciphers['Ordinal']);
            results.innerHTML += '<p>hello in Ordinal: ' + helloResult.totalValue + ' (should be 52)</p>';
            
            // Test "HELLO" in Ordinal - should also be 52
            const HELLOResult = processTextWithCipher('HELLO', ciphers['Ordinal']);
            results.innerHTML += '<p>HELLO in Ordinal: ' + HELLOResult.totalValue + ' (should be 52)</p>';
            
            // Test "hello" in Reduction - should be 25 (8+5+3+3+6)
            const helloReductionResult = processTextWithCipher('hello', ciphers['Reduction']);
            results.innerHTML += '<p>hello in Reduction: ' + helloReductionResult.totalValue + ' (should be 25)</p>';
            
            // Test with numbers "hello123" - should be 52+1+2+3=58
            const helloNumbersResult = processTextWithCipher('hello123', ciphers['Ordinal']);
            results.innerHTML += '<p>hello123 in Ordinal: ' + helloNumbersResult.totalValue + ' (should be 58)</p>';
            
        } else {
            results.innerHTML = '<p>Ciphers not loaded!</p>';
        }
    </script>
</body>
</html>
